rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.status == 'active';
    }



    // Helper function to check if user is active
    function isActiveUser() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.status == 'active';
    }

    // Helper function to check if user has specific document permission
    function hasDocumentPermission(permission) {
      return isActiveUser() &&
             permission in get(/databases/$(database)/documents/users/$(request.auth.uid)).data.permissions.documents;
    }



    // Helper function to validate user data structure
    function isValidUserData(data) {
      return data.keys().hasAll(['id', 'fullName', 'email', 'role', 'status', 'permissions']) &&
             data.role in ['admin', 'user'] &&
             data.status in ['active', 'inactive'] &&
             data.permissions.keys().hasAll(['documents', 'categories', 'system']) &&
             data.permissions.documents is list &&
             data.permissions.categories is list &&
             data.permissions.system is list;
    }

    // Users collection with enhanced security and structure validation
    match /users/{userId} {
      // Allow read for own user document (active users only)
      allow read: if isActiveUser() && request.auth.uid == userId;

      // Allow admin to read all users (unlimited pagination)
      allow read: if isAdmin();

      // Allow users to update their own profile (limited fields)
      allow update: if isActiveUser()
        && request.auth.uid == userId
        && request.resource.data.diff(resource.data).affectedKeys().hasOnly(['fullName', 'profileImageUrl', 'lastLogin', 'lastActiveAt', 'loginCount', 'lastStatsUpdate', 'updatedAt']);

      // Allow admin to create/update/delete user data with structure validation
      allow create: if isAdmin()
        && isValidUserData(request.resource.data)
        && request.resource.data.id == userId;

      allow update: if isAdmin()
        && isValidUserData(request.resource.data)
        && request.resource.data.id == userId;

      // ADMIN-ONLY HARD DELETE: Only admin can permanently delete users
      allow delete: if isAdmin();

      // Allow reading users collection for authenticated active users
      allow list: if isActiveUser();

      // Allow admin unlimited user queries
      allow list: if isAdmin();
    }
    
    // Documents collection with enhanced permission-based access
    match /documents/{documentId} {
      // Allow single document read for users with view permission
      allow get: if hasDocumentPermission('view') || isAdmin();

      // ENHANCED: Allow unlimited queries for admin users
      allow list: if isAdmin();

      // Allow collection queries for users with view permission (with limits)
      allow list: if hasDocumentPermission('view')
        && !isAdmin()
        && request.query.limit <= 100;

      // Allow update for document owner with edit permission or admin
      allow update: if (isActiveUser()
        && request.auth.uid == resource.data.uploadedBy
        && hasDocumentPermission('upload'))
        || isAdmin()
        && (
          // Ensure ID consistency is maintained
          !('id' in request.resource.data) ||
          request.resource.data.id == documentId
        );

      // ENHANCED: Allow category assignment for users with upload permission
      // Users can update category field on any document if they have upload permission
      allow update: if hasDocumentPermission('upload')
        && request.resource.data.diff(resource.data).affectedKeys().hasOnly(['category', 'updatedAt']);

      // Allow create for users with upload permission
      allow create: if hasDocumentPermission('upload')
        && request.auth.uid == request.resource.data.uploadedBy
        && request.resource.data.keys().hasAll(['id', 'fileName', 'uploadedAt'])
        && request.resource.data.id == documentId  // Ensure unified ID consistency
        && request.resource.data.status in ['active', 'pending']
        && request.resource.data.fileName is string
        && request.resource.data.fileName.size() > 0
        && request.resource.data.fileName.size() <= 255;



      // ADMIN-ONLY HARD DELETE: Only administrators can permanently delete documents
      // This enforces the admin-only hard delete policy preference
      allow delete: if isAdmin();
    }
    
    // Categories collection with enhanced access control
    match /categories/{categoryId} {
      // Allow single document read for active users
      allow get: if isActiveUser();

      // Allow unlimited queries for categories (small dataset) for active users
      allow list: if isActiveUser();

      // Only admin can create/update categories with validation
      allow create: if isAdmin()
        && request.resource.data.keys().hasAll(['id', 'name'])
        && request.resource.data.id == categoryId
        && request.resource.data.name is string
        && request.resource.data.name.size() > 0
        && request.resource.data.name.size() <= 100;

      // Admin can update all category fields
      allow update: if isAdmin()
        && request.resource.data.id == categoryId;

      // ENHANCED: Allow users with upload permission to update document count only
      // This enables category assignment functionality for regular users
      allow update: if hasDocumentPermission('upload')
        && request.resource.data.diff(resource.data).affectedKeys().hasOnly(['documentCount', 'updatedAt']);

      // ADMIN-ONLY HARD DELETE: Only admin can permanently delete categories
      allow delete: if isAdmin();
    }

    // Activities collection with enhanced access control
    match /activities/{activityId} {
      // Allow single document read for active users
      allow get: if isActiveUser();

      // Allow collection queries for recent activities with pagination
      allow list: if isActiveUser()
        && request.query.limit <= 50;

      // Allow unlimited activity queries for admin
      allow list: if isAdmin();

      // Allow create for active users with validation
      allow create: if isActiveUser()
        && request.resource.data.keys().hasAll(['type', 'userId', 'timestamp'])
        && request.resource.data.userId == request.auth.uid
        && request.resource.data.type is string
        && request.resource.data.type.size() > 0;

      // ADMIN-ONLY HARD DELETE: Only admin can delete activities (for cleanup)
      allow delete: if isAdmin();
    }

    // Backend Firebase Cloud Functions collections
    // These collections are used by Firebase Cloud Functions for backend operations

    // Upload statistics collection for real-time file count tracking (Backend)
    match /upload-statistics/{statId} {
      // Allow read for active users
      allow read: if isActiveUser();

      // Allow write for active users (for real-time updates)
      allow write: if isActiveUser();

      // Allow admin full access
      allow read, write, delete: if isAdmin();
    }

    // File validation logs collection for security monitoring (Backend)
    match /file-validation-logs/{logId} {
      // Allow read for admin only
      allow read: if isAdmin();

      // Allow create for active users (for logging validation results)
      allow create: if isActiveUser();

      // ADMIN-ONLY HARD DELETE: Only admin can delete old logs
      allow delete: if isAdmin();
    }

    // Duplicate file detection cache (Backend)
    match /duplicate-cache/{cacheId} {
      // Allow read/write for active users (for duplicate detection)
      allow read, write: if isActiveUser();

      // Allow admin full access including hard delete
      allow read, write, delete: if isAdmin();
    }

    // System collections with enhanced security
    match /system-logs/{logId} {
      // Allow read for admin only
      allow read: if isAdmin();

      // Allow create for active users (for system logging)
      allow create: if isActiveUser();

      // ADMIN-ONLY HARD DELETE: Only admin can delete old logs
      allow delete: if isAdmin();
    }

    match /statistics-cache/{cacheId} {
      // Allow read for active users
      allow read: if isActiveUser();

      // Allow write for active users (for cache management)
      allow write: if isActiveUser();

      // ADMIN-ONLY HARD DELETE: Only admin can delete cache entries
      allow delete: if isAdmin();
    }

    match /sync-operations/{syncId} {
      // Allow read for active users
      allow read: if isActiveUser();

      // Allow write for active users (for sync operations)
      allow write: if isActiveUser();

      // Allow admin full access including hard delete
      allow read, write, delete: if isAdmin();
    }

    match /sync-errors/{errorId} {
      // Allow read for admin only
      allow read: if isAdmin();

      // Allow create for active users (for error logging)
      allow create: if isActiveUser();

      // ADMIN-ONLY HARD DELETE: Only admin can delete old errors
      allow delete: if isAdmin();
    }

    match /critical-errors/{errorId} {
      // Allow read for admin only
      allow read: if isAdmin();

      // Allow create for active users (for critical error logging)
      allow create: if isActiveUser();

      // ADMIN-ONLY HARD DELETE: Only admin can delete old errors
      allow delete: if isAdmin();
    }

    match /_health_check/{checkId} {
      // Allow read/write for active users (for health monitoring)
      allow read, write: if isActiveUser();

      // Allow admin full access including hard delete
      allow read, write, delete: if isAdmin();
    }

    // Recycle Bin collection for soft-deleted documents
    match /recycle_bin/{documentId} {
      // Allow read for users who can view the original document or admin
      allow get: if hasDocumentPermission('view') || isAdmin();

      // Allow collection queries for users with view permission (with limits)
      allow list: if hasDocumentPermission('view')
        && !isAdmin()
        && request.query.limit <= 50;

      // Allow unlimited queries for admin users
      allow list: if isAdmin();

      // Allow create when moving documents to recycle bin (users with delete permission)
      allow create: if isActiveUser()
        && hasDocumentPermission('delete')
        && request.resource.data.keys().hasAll(['originalDocumentId', 'deletedBy', 'deletedAt', 'originalLocation'])
        && request.resource.data.deletedBy == request.auth.uid;

      // Allow update for restore operations (users with delete permission)
      allow update: if isActiveUser()
        && hasDocumentPermission('delete');

      // ADMIN-ONLY HARD DELETE: Only admin can permanently delete from recycle bin
      allow delete: if isAdmin();
    }

    // Favorites collection for user's favorite folders
    match /favorites/{favoriteId} {
      // Allow read for the owner of the favorite or admin
      allow get: if isActiveUser()
        && (resource.data.userId == request.auth.uid || isAdmin());

      // Allow collection queries for own favorites only (with limits)
      allow list: if isActiveUser()
        && !isAdmin()
        && request.query.limit <= 100
        && request.auth.uid in request.query.where.userId;

      // Allow unlimited queries for admin users
      allow list: if isAdmin();

      // Allow create for active users (own favorites only)
      allow create: if isActiveUser()
        && request.resource.data.keys().hasAll(['userId', 'folderPath', 'folderName', 'addedAt'])
        && request.resource.data.userId == request.auth.uid;

      // Allow update/delete for own favorites or admin
      allow update, delete: if isActiveUser()
        && (resource.data.userId == request.auth.uid || isAdmin());
    }

    // Storage history collection for storage analytics
    match /storage_history/{historyId} {
      // Allow read for active users (for storage charts and analytics)
      allow get: if isActiveUser();

      // Allow collection queries for active users with limits
      allow list: if isActiveUser()
        && !isAdmin()
        && request.query.limit <= 100;

      // Allow unlimited queries for admin users
      allow list: if isAdmin();

      // Allow create for active users (for recording storage snapshots)
      allow create: if isActiveUser()
        && request.resource.data.keys().hasAll(['timestamp', 'totalBytes', 'fileCount']);

      // ADMIN-ONLY HARD DELETE: Only admin can delete storage history
      allow delete: if isAdmin();
    }

    // Enhanced documents collection rules for statistics
    // Additional rules for statistics calculation (more permissive for count queries)
    match /documents/{documentId} {
      // Allow count queries for authenticated users (for statistics)
      allow read: if request.auth != null && request.query.limit <= 1000;
    }
  }
}
