import 'package:flutter/material.dart';

/// Activity type constants and configurations
class ActivityConstants {
  // Activity type strings
  static const String login = 'login';
  static const String logout = 'logout';
  static const String upload = 'upload';
  static const String download = 'download';
  static const String delete = 'delete';
  static const String view = 'view';
  static const String create = 'create';
  static const String edit = 'edit';
  static const String share = 'share';
  static const String copy = 'copy';
  static const String move = 'move';
  static const String rename = 'rename';
  static const String createUser = 'create_user';
  static const String updateUser = 'update_user';
  static const String deleteUser = 'delete_user';
  static const String categoryCreate = 'category_create';
  static const String categoryUpdate = 'category_update';
  static const String categoryDelete = 'category_delete';
  static const String suspiciousActivity = 'suspicious_activity';
  static const String security = 'security';

  /// All valid activity types
  static const List<String> validActivityTypes = [
    login,
    logout,
    upload,
    download,
    delete,
    view,
    create,
    edit,
    share,
    copy,
    move,
    rename,
    createUser,
    updateUser,
    deleteUser,
    categoryCreate,
    categoryUpdate,
    categoryDelete,
    suspiciousActivity,
    security,
  ];

  /// User-initiated activity types (excludes system-generated)
  static const List<String> userInitiatedTypes = [
    login,
    logout,
    upload,
    download,
    delete,
    view,
    create,
    edit,
    share,
    copy,
    move,
    rename,
    createUser,
    updateUser,
    deleteUser,
    categoryCreate,
    categoryUpdate,
    categoryDelete,
    suspiciousActivity,
    security,
  ];

  /// Activity icons mapping
  static const Map<String, IconData> activityIcons = {
    login: Icons.login,
    logout: Icons.logout,
    upload: Icons.cloud_upload,
    download: Icons.cloud_download,
    delete: Icons.delete,
    view: Icons.visibility,
    create: Icons.add_circle,
    edit: Icons.edit,
    share: Icons.share,
    copy: Icons.copy,
    move: Icons.drive_file_move,
    rename: Icons.drive_file_rename_outline,
    createUser: Icons.person_add,
    updateUser: Icons.person_outline,
    deleteUser: Icons.person_remove,
    categoryCreate: Icons.create_new_folder,
    categoryUpdate: Icons.folder_open,
    categoryDelete: Icons.folder_delete,
    suspiciousActivity: Icons.warning,
    security: Icons.security,
  };

  /// Activity colors mapping
  static const Map<String, Color> activityColors = {
    login: Colors.green,
    logout: Colors.orange,
    upload: Colors.blue,
    download: Colors.indigo,
    delete: Colors.red,
    view: Colors.grey,
    create: Colors.green,
    edit: Colors.amber,
    share: Colors.teal,
    copy: Colors.cyan,
    move: Colors.purple,
    rename: Colors.deepOrange,
    createUser: Colors.lightGreen,
    updateUser: Colors.lightBlue,
    deleteUser: Colors.redAccent,
    categoryCreate: Colors.greenAccent,
    categoryUpdate: Colors.blueAccent,
    categoryDelete: Colors.red,
    suspiciousActivity: Colors.red,
    security: Colors.purple,
  };

  /// Activity display names
  static const Map<String, String> activityDisplayNames = {
    login: 'Login',
    logout: 'Logout',
    upload: 'Upload',
    download: 'Download',
    delete: 'Delete',
    view: 'View',
    create: 'Create',
    edit: 'Edit',
    share: 'Share',
    copy: 'Copy',
    move: 'Move',
    rename: 'Rename',
    createUser: 'Create User',
    updateUser: 'Update User',
    deleteUser: 'Delete User',
    categoryCreate: 'Create Category',
    categoryUpdate: 'Update Category',
    categoryDelete: 'Delete Category',
    suspiciousActivity: 'Suspicious Activity',
    security: 'Security Action',
  };

  /// Get icon for activity type
  static IconData getIcon(String type) {
    return activityIcons[type] ?? Icons.info;
  }

  /// Get color for activity type
  static Color getColor(String type) {
    return activityColors[type] ?? Colors.grey;
  }

  /// Get display name for activity type
  static String getDisplayName(String type) {
    return activityDisplayNames[type] ?? type;
  }

  /// Check if activity type is user-initiated
  static bool isUserInitiated(String type) {
    return userInitiatedTypes.contains(type);
  }

  /// Check if activity type is valid
  static bool isValidType(String type) {
    return validActivityTypes.contains(type);
  }

  /// Get activity category
  static String getCategory(String type) {
    switch (type) {
      case login:
      case logout:
        return 'Authentication';
      case upload:
      case download:
      case delete:
      case view:
      case share:
      case copy:
      case move:
      case rename:
        return 'File Operations';
      case create:
      case edit:
        return 'Document Management';
      case createUser:
      case updateUser:
      case deleteUser:
        return 'User Management';
      case categoryCreate:
      case categoryUpdate:
      case categoryDelete:
        return 'Category Management';
      case suspiciousActivity:
      case security:
        return 'Security';
      default:
        return 'Other';
    }
  }

  /// Pagination limits
  static const int defaultPageSize = 25;
  static const int maxPageSizeUser = 50;
  static const int maxPageSizeAdmin = 100;

  /// Collection name
  static const String collectionName = 'activities';

  /// Required fields for activity creation
  static const List<String> requiredFields = [
    'type',
    'userId',
    'timestamp',
    'description',
  ];

  /// Optional fields
  static const List<String> optionalFields = [
    'userName',
    'userEmail',
    'userRole',
    'documentId',
    'categoryId',
    'isSuspicious',
    'ipAddress',
    'userAgent',
    'details',
  ];
}
